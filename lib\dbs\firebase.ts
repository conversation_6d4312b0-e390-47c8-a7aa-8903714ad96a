import { cert, getApp, getApps, initializeApp } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import serviceAccount from '../../sample-firebase-keys.json';
import { SessionProps, UserData, CheckoutIdData, FeeRuleData } from '../../types';

const app = getApps().length
  ? getApp()
  : initializeApp({ credential: cert(serviceAccount as any) });
const db = getFirestore(app);

// Firestore data management functions

// Use setUser for storing global user data (persists between installs)
export async function setUser({ user }: SessionProps) {
    if (!user) return null;

    const { email, id, username } = user;
    const ref = db.collection('user').doc(String(id));
    const data: UserData = { email };

    if (username) {
        data.username = username;
    }

    await ref.set(data, {merge: true });
}

export async function setStore(session: SessionProps) {
    const {
        access_token: accessToken,
        context,
        scope,
        user: { id },
    } = session;
    // Only set on app install or update
    if (!accessToken || !scope) return null;

    const storeHash = context?.split('/')[1] || '';
    const ref = db.collection('store').doc(storeHash);
    const data = { accessToken, adminId: id, scope };

    await ref.set(data);
}

export async function setScriptUuid(storeHash: string, scriptUuid: string) {
    if (!storeHash || !scriptUuid) return null;

    const ref = db.collection('store').doc(storeHash);
    await ref.update({ scriptUuid });
}

export async function getScriptUuid(storeHash: string): Promise<string | null> {
    if (!storeHash) return null;

    const storeDoc = await db.collection('store').doc(storeHash).get();
    return storeDoc.data()?.scriptUuid ?? null;
}

// User management for multi-user apps
// Use setStoreUser for storing store specific variables
export async function setStoreUser(session: SessionProps) {
    const {
        access_token: accessToken,
        context,
        owner,
        sub,
        user: { id: userId },
    } = session;
    if (!userId) return null;

    const contextString = context ?? sub;
    const storeHash = contextString?.split('/')[1] || '';
    const documentId = `${userId}_${storeHash}`; // users can belong to multiple stores
    const ref = db.collection('storeUsers').doc(documentId);
    const storeUser = await ref.get();

    // Set admin (store owner) if installing/ updating the app
    // https://developer.bigcommerce.com/api-docs/apps/guide/users
    if (accessToken) {
        // Create a new admin user if none exists
        if (!storeUser.exists) {
            await ref.set({ storeHash, isAdmin: true });
        } else if (!storeUser.data()?.isAdmin) {
            await ref.update({ isAdmin: true });
        }
    } else {
        // Create a new user if it doesn't exist
        if (!storeUser.exists) {
            await ref.set({ storeHash, isAdmin: owner.id === userId });
        }
    }
}

export async function deleteUser({ context, user, sub }: SessionProps) {
    const contextString = context ?? sub;
    const storeHash = contextString?.split('/')[1] || '';
    const docId = `${user?.id}_${storeHash}`;
    const ref = db.collection('storeUsers').doc(docId);

    await ref.delete();
}

export async function hasStoreUser(storeHash: string, userId: string) {
    if (!storeHash || !userId) return false;

    const docId = `${userId}_${storeHash}`;
    const userDoc = await db.collection('storeUsers').doc(docId).get();

    return userDoc.exists;
}

export async function getStoreToken(storeHash: string) {
    if (!storeHash) return null;
    const storeDoc = await db.collection('store').doc(storeHash).get();

    return storeDoc.data()?.accessToken ?? null;
}

export async function deleteStore({ store_hash: storeHash }: SessionProps) {
    const ref = db.collection('store').doc(storeHash);

    await ref.delete();
}

export async function storeCheckoutId(data: CheckoutIdData) {
    const { checkoutId, storeHash, timestamp, customer, channel } = data;

    if (!checkoutId || !storeHash) {
        throw new Error('CheckoutId and storeHash are required');
    }

    // Filter channel data to only include active channels with just id and name
    let processedChannel = null;
    if (channel && Array.isArray(channel)) {
        processedChannel = channel
            .filter(ch => ch.is_active === true)
            .map(ch => ({
                id: ch.id
            }));
    }

    const ref = db.collection('stores').doc(storeHash)
        .collection('checkoutIds').doc(checkoutId);

    await ref.set({
        checkoutId,
        storeHash,
        timestamp: timestamp || Date.now(),
        customer: customer || null,
        channel: processedChannel, // Store only filtered channel data
    });
}

export async function getRecentCheckoutIds(storeHash: string, limit: number = 5): Promise<CheckoutIdData[]> {
    if (!storeHash) {
        return [];
    }

    const ref = db.collection('stores').doc(storeHash)
        .collection('checkoutIds')
        .orderBy('timestamp', 'desc')
        .limit(limit);

    const snapshot = await ref.get();

    return snapshot.docs.map(doc => doc.data() as CheckoutIdData);
}

export async function storeFeeRule(data: FeeRuleData): Promise<string> {
    const { storeHash, ...feeRuleData } = data;

    if (!storeHash) {
        throw new Error('StoreHash is required');
    }

    // Validate required fields
    if (!feeRuleData.name || !feeRuleData.type || !feeRuleData.display_name || feeRuleData.cost === undefined) {
        throw new Error('Name, type, display_name, and cost are required fields');
    }

    // Validate type
    if (!['percentage', 'fixed'].includes(feeRuleData.type)) {
        throw new Error('Type must be either "percentage" or "fixed"');
    }

    // Validate cost
    if (typeof feeRuleData.cost !== 'number' || feeRuleData.cost < 0) {
        throw new Error('Cost must be a non-negative number');
    }

    const ref = db.collection('stores').doc(storeHash)
        .collection('feeRules').doc();

    // Filter out undefined values to prevent Firestore errors
    const cleanedFeeRuleData: any = {
        name: feeRuleData.name,
        type: feeRuleData.type,
        display_name: feeRuleData.display_name,
        cost: feeRuleData.cost,
        active: feeRuleData.active,
        created_at: Date.now(),
    };

    // Only add optional fields if they have values
    if (feeRuleData.source !== undefined && feeRuleData.source !== '') {
        cleanedFeeRuleData.source = feeRuleData.source;
    }

    if (feeRuleData.tax_class_id !== undefined && typeof feeRuleData.tax_class_id === 'number') {
        cleanedFeeRuleData.tax_class_id = feeRuleData.tax_class_id;
    }

    // Add filter fields (including empty arrays)
    if (feeRuleData.selectedBrands !== undefined && Array.isArray(feeRuleData.selectedBrands)) {
        cleanedFeeRuleData.selectedBrands = feeRuleData.selectedBrands;
    } else {
        // Default to empty array if not provided
        cleanedFeeRuleData.selectedBrands = [];
    }

    if (feeRuleData.selectedCategories !== undefined && Array.isArray(feeRuleData.selectedCategories)) {
        cleanedFeeRuleData.selectedCategories = feeRuleData.selectedCategories;
    } else {
        // Default to empty array if not provided
        cleanedFeeRuleData.selectedCategories = [];
    }

    if (feeRuleData.selectedCustomerGroups !== undefined && Array.isArray(feeRuleData.selectedCustomerGroups)) {
        cleanedFeeRuleData.selectedCustomerGroups = feeRuleData.selectedCustomerGroups;
    } else {
        // Default to empty array if not provided
        cleanedFeeRuleData.selectedCustomerGroups = [];
    }

    // Add channels filter field
    if (feeRuleData.selectedChannels !== undefined && Array.isArray(feeRuleData.selectedChannels)) {
        cleanedFeeRuleData.selectedChannels = feeRuleData.selectedChannels;
    } else {
        // Default to empty array if not provided
        cleanedFeeRuleData.selectedChannels = [];
    }

    // Add products filter field
    if (feeRuleData.selectedProducts !== undefined && Array.isArray(feeRuleData.selectedProducts)) {
        cleanedFeeRuleData.selectedProducts = feeRuleData.selectedProducts;
        console.log(`Storing fee rule with ${feeRuleData.selectedProducts.length} selected products:`, feeRuleData.selectedProducts);
    } else {
        // Default to empty array if not provided
        cleanedFeeRuleData.selectedProducts = [];
        console.log('Storing fee rule with empty selectedProducts array (no products provided)');
    }

    await ref.set(cleanedFeeRuleData);

    return ref.id;
}

export async function getFeeRules(storeHash: string): Promise<FeeRuleData[]> {
    if (!storeHash) {
        return [];
    }

    const ref = db.collection('stores').doc(storeHash)
        .collection('feeRules')
        .orderBy('created_at', 'desc');

    const snapshot = await ref.get();

    return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
    } as FeeRuleData));
}

export async function getFeeRule(storeHash: string, feeRuleId: string): Promise<FeeRuleData | null> {
    if (!storeHash || !feeRuleId) {
        return null;
    }

    const ref = db.collection('stores').doc(storeHash)
        .collection('feeRules').doc(feeRuleId);

    const doc = await ref.get();

    if (!doc.exists) {
        return null;
    }

    return {
        id: doc.id,
        ...doc.data()
    } as FeeRuleData;
}

export async function updateFeeRule(storeHash: string, feeRuleId: string, data: Partial<FeeRuleData>): Promise<void> {
    if (!storeHash || !feeRuleId) {
        throw new Error('StoreHash and feeRuleId are required');
    }

    // Remove fields that shouldn't be updated
    const { id, storeHash: _, created_at, ...updateData } = data;

    // Filter out undefined values to prevent Firestore errors
    const cleanedUpdateData: any = {};

    // Add defined fields
    if (updateData.name !== undefined) cleanedUpdateData.name = updateData.name;
    if (updateData.type !== undefined) cleanedUpdateData.type = updateData.type;
    if (updateData.display_name !== undefined) cleanedUpdateData.display_name = updateData.display_name;
    if (updateData.cost !== undefined) cleanedUpdateData.cost = updateData.cost;
    if (updateData.active !== undefined) cleanedUpdateData.active = updateData.active;

    // Only add optional fields if they have values
    if (updateData.source !== undefined && updateData.source !== '') {
        cleanedUpdateData.source = updateData.source;
    }

    if (updateData.tax_class_id !== undefined && typeof updateData.tax_class_id === 'number') {
        cleanedUpdateData.tax_class_id = updateData.tax_class_id;
    }

    // Add filter fields if they exist (including empty arrays)
    if (updateData.selectedBrands !== undefined && Array.isArray(updateData.selectedBrands)) {
        cleanedUpdateData.selectedBrands = updateData.selectedBrands;
    }

    if (updateData.selectedCategories !== undefined && Array.isArray(updateData.selectedCategories)) {
        cleanedUpdateData.selectedCategories = updateData.selectedCategories;
    }

    if (updateData.selectedCustomerGroups !== undefined && Array.isArray(updateData.selectedCustomerGroups)) {
        cleanedUpdateData.selectedCustomerGroups = updateData.selectedCustomerGroups;
    }

    // Add channels filter field
    if (updateData.selectedChannels !== undefined && Array.isArray(updateData.selectedChannels)) {
        cleanedUpdateData.selectedChannels = updateData.selectedChannels;
    }

    // Add products filter field
    if (updateData.selectedProducts !== undefined && Array.isArray(updateData.selectedProducts)) {
        cleanedUpdateData.selectedProducts = updateData.selectedProducts;
        console.log(`Updating fee rule with ${updateData.selectedProducts.length} selected products:`, updateData.selectedProducts);
    } else {
        console.log('Updating fee rule: selectedProducts field not provided or invalid');
    }

    // Add updated timestamp
    cleanedUpdateData.updated_at = Date.now();

    const ref = db.collection('stores').doc(storeHash)
        .collection('feeRules').doc(feeRuleId);

    await ref.update(cleanedUpdateData);
}

export async function deleteFeeRule(storeHash: string, feeRuleId: string): Promise<void> {
    if (!storeHash || !feeRuleId) {
        throw new Error('StoreHash and feeRuleId are required');
    }

    const ref = db.collection('stores').doc(storeHash)
        .collection('feeRules').doc(feeRuleId);

    await ref.delete();
}

/**
 * Update fee rule with BigCommerce fee tracking information
 */
export async function updateFeeRuleTracking(
    storeHash: string,
    feeRuleId: string,
    bigCommerceFeeId: string,
    checkoutId: string,
    appliedCost: number
): Promise<void> {
    if (!storeHash || !feeRuleId || !bigCommerceFeeId || !checkoutId) {
        throw new Error('StoreHash, feeRuleId, bigCommerceFeeId, and checkoutId are required');
    }

    const ref = db.collection('stores').doc(storeHash)
        .collection('feeRules').doc(feeRuleId);

    // Get current fee rule to preserve existing history
    const doc = await ref.get();
    if (!doc.exists) {
        throw new Error(`Fee rule ${feeRuleId} not found`);
    }

    const currentData = doc.data();
    const existingHistory = currentData?.appliedFeeHistory || [];

    // Create new applied fee record
    const newAppliedFeeRecord = {
        feeId: bigCommerceFeeId,
        checkoutId: checkoutId,
        appliedAt: Date.now(),
        cost: appliedCost
    };

    // Update with new tracking information
    const updateData = {
        appliedFeeId: bigCommerceFeeId, // Most recent fee ID
        lastAppliedAt: Date.now(),
        appliedFeeHistory: [...existingHistory, newAppliedFeeRecord], // Append to history
        updated_at: Date.now()
    };

    await ref.update(updateData);

    console.log(`Updated fee rule ${feeRuleId} with BigCommerce fee ID: ${bigCommerceFeeId}`);
}
