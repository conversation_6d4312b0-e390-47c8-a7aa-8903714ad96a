// Using native fetch API to maintain consistency with the rest of the codebase

const { APP_URL } = process.env;

/**
 * Checks if script injection is properly configured
 * @returns boolean indicating if script injection can be performed
 */
export function isScriptInjectionConfigured(): boolean {
    const appUrl = APP_URL || 'http://localhost:3000';

    try {
        new URL(appUrl);
        return true;
    } catch {
        return false;
    }
}

/**
 * Gets the configured app URL with validation
 * @returns string - the validated app URL
 * @throws Error if URL is invalid
 */
export function getValidatedAppUrl(): string {
    const appUrl = APP_URL || 'http://localhost:3000';

    try {
        new URL(appUrl);
        return appUrl;
    } catch (urlError) {
        throw new Error(`Invalid APP_URL configuration: ${appUrl}. Must be a valid URL.`);
    }
}

/**
 * Interface for script injection response from BigCommerce API
 */
interface ScriptInjectionResponse {
    data: {
        uuid: string;
        name: string;
        description: string;
        html: string;
        src?: string;
        auto_uninstall: boolean;
        load_method: string;
        location: string;
        visibility: string;
        kind: string;
        api_client_id: string;
        date_created: string;
        date_modified: string;
        consent_category: string;
    };
}

/**
 * Interface for script deletion from BigCommerce API
 */
interface ScriptDeletionResponse {
    data: any;
}

/**
 * Injects a checkout tracking script into a BigCommerce store
 * The script captures checkout data and sends it to the app's receive-checkout-id endpoint
 * 
 * @param storeHash - The store hash for the BigCommerce store
 * @param accessToken - The access token for BigCommerce API authentication
 * @returns Promise<string> - Returns the script UUID on success
 */
export async function injectCheckoutScript(storeHash: string, accessToken: string): Promise<string> {
    // Validate input parameters
    if (!storeHash || typeof storeHash !== 'string') {
        throw new Error('Invalid storeHash: must be a non-empty string');
    }

    if (!accessToken || typeof accessToken !== 'string') {
        throw new Error('Invalid accessToken: must be a non-empty string');
    }

    // Get validated app URL
    const appUrl = getValidatedAppUrl();

    console.log(`🚀 Injecting checkout script for store: ${storeHash}`);
    console.log(`📡 App URL: ${appUrl}`);

    const html = `
<script>
  (function () {
    let checkoutId = {{{json cart_id}}};
    let storeHash = {{{json settings.store_hash}}};
    let customer = {{{json customer}}};
    let channel = {{{json theme_settings.channels }}};

    console.log("Sending checkoutId & storeHash to app:-", checkoutId, storeHash);
    console.log("Sending customer to app:-", customer);
    console.log("Sending channel to app:-", channel);

    if (checkoutId && storeHash) {
      fetch('${appUrl}/api/receive-checkout-id', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          checkoutId: checkoutId,
          storeHash: storeHash,
          customer: customer,
          channel: channel
        })
      })
      .then(res => res.json())
      .then(data => console.log('Data sent successfully:', data))
      .catch(err => console.error('Error sending data:', err));
    }
  })();
</script>
`;

    const payload = {
        name: "Checkout ID Sender Script",
        description: "Inline script that sends checkout ID & store info to app",
        html: html,
        auto_uninstall: true,
        load_method: "default",
        location: "footer",
        visibility: "checkout",
        kind: "script_tag",
        consent_category: "essential"
    };

    try {
        const response = await fetch(
            `https://api.bigcommerce.com/stores/${storeHash}/v3/content/scripts`,
            {
                method: 'POST',
                headers: {
                    'X-Auth-Token': accessToken,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(payload)
            }
        );

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error("❌ Script injection failed:", {
                status: response.status,
                statusText: response.statusText,
                data: errorData
            });
            throw new Error(`Failed to inject checkout script: ${errorData.title || response.statusText}`);
        }

        const responseData: ScriptInjectionResponse = await response.json();
        const scriptUuid = responseData.data.uuid;

        console.log("✅ Script injected successfully:", {
            uuid: scriptUuid,
            name: responseData.data.name,
            location: responseData.data.location,
            visibility: responseData.data.visibility
        });

        return scriptUuid;
    } catch (err: any) {
        if (err.message.includes('Failed to inject checkout script:')) {
            throw err; // Re-throw our formatted error
        }

        console.error("❌ Script injection failed:", {
            message: err.message
        });

        // Re-throw with more context
        throw new Error(`Failed to inject checkout script: ${err.message}`);
    }
}

/**
 * Removes a checkout tracking script from a BigCommerce store
 * 
 * @param storeHash - The store hash for the BigCommerce store
 * @param accessToken - The access token for BigCommerce API authentication
 * @param scriptUuid - The UUID of the script to remove
 * @returns Promise<void>
 */
export async function removeCheckoutScript(storeHash: string, accessToken: string, scriptUuid: string): Promise<void> {
    // Validate input parameters
    if (!storeHash || typeof storeHash !== 'string') {
        throw new Error('Invalid storeHash: must be a non-empty string');
    }

    if (!accessToken || typeof accessToken !== 'string') {
        throw new Error('Invalid accessToken: must be a non-empty string');
    }

    if (!scriptUuid || typeof scriptUuid !== 'string') {
        throw new Error('Invalid scriptUuid: must be a non-empty string');
    }

    console.log(`🗑️ Removing checkout script for store: ${storeHash}, script UUID: ${scriptUuid}`);

    try {
        const response = await fetch(
            `https://api.bigcommerce.com/stores/${storeHash}/v3/content/scripts/${scriptUuid}`,
            {
                method: 'DELETE',
                headers: {
                    'X-Auth-Token': accessToken,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            }
        );

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error("❌ Script removal failed:", {
                uuid: scriptUuid,
                status: response.status,
                statusText: response.statusText,
                data: errorData
            });
            throw new Error(`Failed to remove checkout script: ${errorData.title || response.statusText}`);
        }

        console.log("✅ Script removed successfully:", {
            uuid: scriptUuid,
            status: response.status
        });
    } catch (err: any) {
        if (err.message.includes('Failed to remove checkout script:')) {
            throw err; // Re-throw our formatted error
        }

        console.error("❌ Script removal failed:", {
            uuid: scriptUuid,
            message: err.message
        });

        // Re-throw with more context
        throw new Error(`Failed to remove checkout script: ${err.message}`);
    }
}

/**
 * Lists all scripts for a BigCommerce store (useful for debugging)
 * 
 * @param storeHash - The store hash for the BigCommerce store
 * @param accessToken - The access token for BigCommerce API authentication
 * @returns Promise<any[]> - Returns array of scripts
 */
export async function listScripts(storeHash: string, accessToken: string): Promise<any[]> {
    // Validate input parameters
    if (!storeHash || typeof storeHash !== 'string') {
        throw new Error('Invalid storeHash: must be a non-empty string');
    }

    if (!accessToken || typeof accessToken !== 'string') {
        throw new Error('Invalid accessToken: must be a non-empty string');
    }

    console.log(`📋 Listing scripts for store: ${storeHash}`);

    try {
        const response = await fetch(
            `https://api.bigcommerce.com/stores/${storeHash}/v3/content/scripts`,
            {
                method: 'GET',
                headers: {
                    'X-Auth-Token': accessToken,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            }
        );

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error("❌ Failed to list scripts:", {
                status: response.status,
                statusText: response.statusText,
                data: errorData
            });
            throw new Error(`Failed to list scripts: ${errorData.title || response.statusText}`);
        }

        const responseData = await response.json();
        console.log(`✅ Found ${responseData.data.length} scripts`);
        return responseData.data;
    } catch (err: any) {
        if (err.message.includes('Failed to list scripts:')) {
            throw err; // Re-throw our formatted error
        }

        console.error("❌ Failed to list scripts:", {
            message: err.message
        });

        throw new Error(`Failed to list scripts: ${err.message}`);
    }
}
