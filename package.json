{"name": "sample-app-nodejs", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "next", "build": "next build", "start": "next start -p $PORT", "test": "jest --updateSnapshot", "lint": "eslint . --ext .ts,.tsx,.js", "db:setup": "node scripts/db.js"}, "keywords": [], "author": "", "license": "ISC", "engines": {"node": ">=18 <20", "npm": ">=8 <10"}, "dependencies": {"@bigcommerce/big-design": "^0.36.2", "@bigcommerce/big-design-icons": "^0.23.1", "@bigcommerce/big-design-theme": "^0.19.1", "axios": "^1.10.0", "dotenv": "^16.3.0", "firebase-admin": "^13.3.0", "jsonwebtoken": "^9.0.1", "mysql2": "^3.9.4", "next": "^13.5.4", "node-bigcommerce": "github:bigcommerce/node-bigcommerce", "react": "^18.2.0", "react-dom": "^18.2.0", "styled-components": "^5.3.11", "swr": "^1.3.0"}, "devDependencies": {"@testing-library/dom": "^7.31.2", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.0.0", "@types/jest": "^27.5.2", "@types/node": "^18.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.1", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^5.30.0", "@typescript-eslint/parser": "^5.30.0", "babel-jest": "^27.5.1", "babel-plugin-styled-components": "^2.0.7", "eslint": "^8.19.0", "eslint-import-resolver-typescript": "^3.2.5", "eslint-plugin-import": "^2.26.0", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^28.1.2", "jest-environment-jsdom": "^28.1.2", "typescript": "^4.7.3"}}