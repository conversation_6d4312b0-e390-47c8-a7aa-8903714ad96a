import { NextApiRequest, NextApiResponse } from 'next';
import db from '../../lib/db';
import { applyFeesToCheckout, FeeApplicationResult } from '../../lib/fee-application';

export default async function receiveCheckoutId(req: NextApiRequest, res: NextApiResponse) {
    // Set CORS headers to allow requests from BigCommerce stores
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    // Handle preflight OPTIONS request
    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        res.status(405).json({ error: `Method ${req.method} Not Allowed` });
        return;
    }

    try {
        const { checkoutId, storeHash, customer, channel } = req.body;

        // Enhanced logging for script injection data
        console.log('📨 Received checkout data from injected script:', {
            checkoutId,
            storeHash,
            hasCustomer: !!customer,
            customerData: customer ? {
                id: customer.id,
                email: customer.email,
                firstName: customer.first_name,
                lastName: customer.last_name
            } : null,
            hasChannel: !!channel,
            channelData: channel ? (Array.isArray(channel) ? channel.map(ch => ({ id: ch.id, name: ch.name })) : channel) : null,
            timestamp: new Date().toISOString()
        });

        // Validate required fields
        if (!checkoutId || !storeHash) {
            console.error('❌ Validation failed: Missing required fields', { checkoutId, storeHash });
            res.status(400).json({
                error: 'Missing required fields: checkoutId and storeHash are required'
            });
            return;
        }

        // Validate data types
        if (typeof checkoutId !== 'string' || typeof storeHash !== 'string') {
            console.error('❌ Validation failed: Invalid data types', {
                checkoutIdType: typeof checkoutId,
                storeHashType: typeof storeHash
            });
            res.status(400).json({
                error: 'Invalid data types: checkoutId and storeHash must be strings'
            });
            return;
        }

        // Store the checkout ID in the database
        console.log(`💾 Storing checkout data for ${checkoutId} in store ${storeHash}`);
        await db.storeCheckoutId({
            checkoutId,
            storeHash,
            timestamp: Date.now(),
            customer: customer || null, // Store customer data if provided
            channel: channel || null, // Store channel data if provided
        });
        console.log(`✅ Successfully stored checkout data for ${checkoutId}`);

        // Apply fees automatically to the new checkout
        let feeApplicationResult: FeeApplicationResult;
        try {
            console.log(`Starting automatic fee application for checkout ${checkoutId} in store ${storeHash}`);
            feeApplicationResult = await applyFeesToCheckout(storeHash, checkoutId, customer, channel);

            if (feeApplicationResult.success) {
                console.log(`Successfully applied ${feeApplicationResult.appliedFees.length} fees to checkout ${checkoutId}`);
            } else {
                console.warn(`Fee application completed with errors for checkout ${checkoutId}:`, feeApplicationResult.errors);
            }
        } catch (error) {
            console.error(`Error during automatic fee application for checkout ${checkoutId}:`, error);
            // Don't fail the entire request if fee application fails
            feeApplicationResult = {
                success: false,
                appliedFees: [],
                errors: [{ feeRule: null, error: error.message }]
            };
        }

        res.status(200).json({
            success: true,
            message: 'Checkout ID and customer data and channel stored successfully',
            data: {
                checkoutId,
                storeHash,
                customerStored: !!customer,
                channelStored: !!channel,
                feeApplication: {
                    success: feeApplicationResult.success,
                    appliedFeesCount: feeApplicationResult.appliedFees.length,
                    errorsCount: feeApplicationResult.errors.length,
                    appliedFees: feeApplicationResult.appliedFees.map(fee => ({
                        id: fee.id,
                        name: fee.name,
                        cost: fee.cost || fee.cost_inc_tax || fee.cost_ex_tax || 0,
                        cost_inc_tax: fee.cost_inc_tax,
                        cost_ex_tax: fee.cost_ex_tax
                    })),
                    errors: feeApplicationResult.errors.map(error => ({
                        feeRuleName: error.feeRule?.name || 'Unknown',
                        error: error.error
                    })),
                    trackingUpdates: feeApplicationResult.trackingUpdates?.map(update => ({
                        feeRuleId: update.feeRuleId,
                        feeRuleName: update.feeRuleName,
                        bigCommerceFeeId: update.bigCommerceFeeId,
                        success: update.success,
                        error: update.error
                    })) || []
                }
            }
        });

    } catch (error) {
        console.error('Error storing checkout ID:', error);
        res.status(500).json({ 
            error: 'Internal server error',
            message: error.message 
        });
    }
}
