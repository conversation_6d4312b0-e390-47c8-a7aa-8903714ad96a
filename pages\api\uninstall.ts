import { NextApiRequest, NextApiResponse } from 'next';
import { getBCVerify, removeDataStore } from '../../lib/auth';
import { removeCheckoutScript } from '../../lib/script-injection';
import db from '../../lib/db';

export default async function uninstall(req: NextApiRequest, res: NextApiResponse) {
    try {
        const session = await getBCVerify(req.query);
        const storeHash = session.store_hash || session.context?.split('/')[1];

        // Remove checkout tracking script before cleaning up data
        if (storeHash) {
            try {
                const accessToken = await db.getStoreToken(storeHash);
                const scriptUuid = await db.getScriptUuid(storeHash);

                if (accessToken && scriptUuid) {
                    console.log(`🗑️ Removing checkout script for store: ${storeHash}, UUID: ${scriptUuid}`);
                    await removeCheckoutScript(storeHash, accessToken, scriptUuid);
                    console.log(`✅ Checkout script removed successfully`);
                } else {
                    console.log(`⚠️ No script UUID found for store ${storeHash}, skipping script removal`);
                }
            } catch (scriptError) {
                // Log the error but don't fail the uninstall
                console.error('⚠️ Failed to remove checkout script during uninstall:', scriptError);
                console.error('Continuing with app uninstall process');
            }
        }

        await removeDataStore(session);
        res.status(200).end();
    } catch (error) {
        const { message, response } = error;
        res.status(response?.status || 500).json({ message });
    }
}
