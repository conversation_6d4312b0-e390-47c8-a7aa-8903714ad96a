// pages/api/webhooks/index.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import { getApps, getApp, initializeApp, cert } from 'firebase-admin/app';
import serviceAccount from 'sample-firebase-keys.json';
import { getFirestore } from 'firebase-admin/firestore';
import { getSession } from '@lib/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        return res.status(405).send('Method Not Allowed');
    }

    const app = getApps().length
        ? getApp()
        : initializeApp({ credential: cert(serviceAccount as any) });
    const db = getFirestore(app);

    const storeHash = req.body?.producer?.split('/')?.[1];
    const checkoutIdsSnapshot = await db.collection('stores').doc(storeHash).collection('checkoutIds').get();

    try {
        const cartId = req.body?.data?.id;
        
        console.log('🛒 Cart Deleted Webhook Triggered');
        console.log('🧾 Deleted Cart ID:', cartId);

        if (checkoutIdsSnapshot.empty) {
        console.log('⚠️ No checkoutId documents found.');
    } else {
        checkoutIdsSnapshot.forEach(doc => {
            const data = doc.data();
            console.log(`🔍 Found checkoutId = ${data.checkoutId}`);

            // Match cartId with checkoutId
            const cartId = req.body?.data?.id;
            if (cartId === data.checkoutId) {
                console.log(`🔥 Match found! Deleting checkoutId ${data.checkoutId} from Firebase`);
                db.collection('stores').doc(storeHash).collection('checkoutIds').doc(doc.id).delete();
            }
        });
    }

        res.status(200).json({ received: true });
    } catch (error) {
        console.error('Error handling webhook:', error);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}
