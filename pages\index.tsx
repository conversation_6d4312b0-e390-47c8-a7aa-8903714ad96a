import { GetServerSideProps } from 'next';
import { NextApiRequest } from 'next';
import { registerCartDeletedWebhook } from '@lib/bigcommerce-api';
import db from '@lib/db'; // Import the db module
import { getSession } from '@lib/auth'; // Import getSession if needed

// This page redirects to Order Fee as the new default
// Using server-side redirect to avoid client-side routing conflicts
const Index = () => {
    // This component should never render since we redirect server-side
    return null;
};

export const getServerSideProps: GetServerSideProps = async ({ query }) => {
    // Preserve all query parameters (especially important for BigCommerce context)
    const queryString = new URLSearchParams(query as Record<string, string>).toString();
    const destination = queryString ? `/order-fee?${queryString}` : '/order-fee';

    try {
        // Get session data from the query context if available
        const session = query.context ? await getSession({ query } as NextApiRequest) : null;
        
        // Get storeHash and accessToken from session or try to get from query
        let storeHash = session?.storeHash;
        let accessToken = session?.accessToken;
        
        // If not available in session, try to extract from query
        if (!storeHash && query.context) {
            const contextParts = String(query.context).split('/');
            if (contextParts.length > 1) {
                storeHash = contextParts[1];
                // If we have storeHash but no accessToken, try to get it from Firebase
                if (!accessToken && storeHash) {
                    accessToken = await db.getStoreToken(storeHash);
                }
            }
        }

        const ngrokURL = process.env.APP_URL;
        const destinationUrl = `${ngrokURL}/api/webhooks`;

        if (storeHash && accessToken) {
            try {
                await registerCartDeletedWebhook(storeHash, accessToken, destinationUrl);
                console.log('Webhook registered in getServerSideProps');
            } catch (err) {
                // console.error('Failed to register webhook:', err);
            }
        } else {
            console.warn('Missing storeHash or accessToken for webhook setup');
        }
    } catch (error) {
        console.error('Error in getServerSideProps:', error);
    }

    return {
        redirect: {
            destination,
            permanent: false, // Use 302 redirect (temporary) since this is a routing preference
        },
    };
};

export default Index;
