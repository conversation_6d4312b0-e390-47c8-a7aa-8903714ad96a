import { SessionProps } from './index';

export interface StoreData {
    accessToken?: string;
    scope?: string;
    storeHash: string;
    scriptUuid?: string;
}

export interface UserData {
    email: string;
    username?: string;
}

export interface CheckoutIdData {
    checkoutId: string;
    storeHash: string;
    timestamp: number;
    customer?: any; // Add customer data field
    channel?:any;
}

export interface AppliedFeeRecord {
    feeId: string;
    checkoutId: string;
    appliedAt: number;
    cost: number;
}

export interface FeeRuleData {
    id?: string;
    name: string;
    type: 'percentage' | 'fixed';
    display_name: string;
    cost: number;
    source?: string;
    tax_class_id?: number;
    active: boolean;
    created_at: number;
    storeHash?: string;
    // Filter fields
    selectedBrands?: number[]; // Array of brand IDs
    selectedCategories?: number[]; // Array of category IDs
    selectedCustomerGroups?: number[]; // Array of customer group IDs
    selectedChannels?: number[]; // Array of channel IDs
    selectedProducts?: number[]; // Array of product IDs
    // Fee tracking fields
    appliedFeeId?: string; // Most recent BigCommerce fee ID
    appliedFeeHistory?: AppliedFeeRecord[]; // History of all applications
    lastAppliedAt?: number; // Timestamp of last application
}

export interface Db {
    hasStoreUser(storeHash: string, userId: string): Promise<boolean> | boolean;
    setUser(session: SessionProps): Promise<void>;
    setStore(session: SessionProps): Promise<void>;
    setStoreUser(session: SessionProps): Promise<void>;
    getStoreToken(storeId: string): Promise<string> | null;
    deleteStore(session: SessionProps): Promise<void>;
    deleteUser(session: SessionProps): Promise<void>;
    storeCheckoutId(data: CheckoutIdData): Promise<void>;
    getRecentCheckoutIds(storeHash: string, limit?: number): Promise<CheckoutIdData[]>;
    storeFeeRule(data: FeeRuleData): Promise<string>;
    getFeeRules(storeHash: string): Promise<FeeRuleData[]>;
    getFeeRule(storeHash: string, feeRuleId: string): Promise<FeeRuleData | null>;
    setScriptUuid(storeHash: string, scriptUuid: string): Promise<void> | null;
    getScriptUuid(storeHash: string): Promise<string | null>;
    updateFeeRule(storeHash: string, feeRuleId: string, data: Partial<FeeRuleData>): Promise<void>;
    deleteFeeRule(storeHash: string, feeRuleId: string): Promise<void>;
    updateFeeRuleTracking(storeHash: string, feeRuleId: string, bigCommerceFeeId: string, checkoutId: string, appliedCost: number): Promise<void>;
}
